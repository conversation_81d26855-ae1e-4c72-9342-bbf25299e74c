@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities */
@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Nathaniel's Event & Decor Design System */
    --background: 210 20% 98%; /* Cloud white background */
    --foreground: 210 12% 16%; /* Soft black for text */

    --card: 0 0% 100%; /* Pure white cards */
    --card-foreground: 210 12% 16%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 12% 16%;

    /* Professional blue accent */
    --primary: 213 94% 68%; /* Beautiful blue #3B82F6 */
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 210 12% 16%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 213 94% 68%; /* Same as primary for consistency */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 213 94% 68%; /* Blue ring to match primary */

    /* Design tokens for the event business */
    --premium-gradient: linear-gradient(135deg, hsl(213 94% 68%), hsl(213 94% 78%));
    --hero-gradient: linear-gradient(135deg, hsl(213 94% 68% / 0.1), hsl(213 94% 78% / 0.05));
    --card-shadow: 0 4px 6px -1px hsl(213 94% 68% / 0.1), 0 2px 4px -1px hsl(213 94% 68% / 0.06);
    --hover-shadow: 0 10px 15px -3px hsl(213 94% 68% / 0.15), 0 4px 6px -2px hsl(213 94% 68% / 0.1);

    --radius: 0.75rem; /* Slightly more rounded for premium feel */

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Search highlighting styles */
  mark {
    @apply bg-primary/20 text-primary font-medium px-0.5 rounded-sm;
  }

  .dark mark {
    @apply bg-primary/30 text-primary-foreground;
  }

  /* Smooth transitions for search modal */
  .search-modal-content {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Prevent layout shift during search */
  .search-results-container {
    @apply min-h-96;
  }

  /* Highlight animation for search results */
  @keyframes pulse-highlight {
    0%, 100% {
      transform: scale(1.02);
      opacity: 1;
    }
    50% {
      transform: scale(1.025);
      opacity: 0.95;
    }
  }

  .animate-pulse-highlight {
    animation: pulse-highlight 2s ease-in-out 3;
  }
}